"""
Integration tests for API endpoints
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
import sys
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.main import app

class TestAPIIntegration:
    """Integration tests for API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_ohlcv_test_connection(self, client):
        """Test exchange connection endpoint"""
        # Test Binance connection
        response = client.get("/api/v1/ohlcv/test-connection?exchange=binance")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert data["exchange"] == "binance"
    
    def test_indicators_available(self, client):
        """Test available indicators endpoint"""
        response = client.get("/api/v1/indicators/available")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "indicators" in data["data"]
        
        indicators = data["data"]["indicators"]
        assert "rsi" in indicators
        assert "macd" in indicators
        assert "ema" in indicators
    
    def test_invalid_endpoints(self, client):
        """Test invalid endpoints return 404"""
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        
        response = client.get("/invalid/path")
        assert response.status_code == 404
    
    def test_api_docs_accessible(self, client):
        """Test that API documentation is accessible"""
        response = client.get("/api/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        response = client.get("/api/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_cors_headers(self, client):
        """Test CORS headers are present"""
        response = client.options("/api/v1/ohlcv/test-connection")
        assert response.status_code == 200
        
        # Check CORS headers
        headers = response.headers
        assert "access-control-allow-origin" in headers
        assert "access-control-allow-methods" in headers
    
    @pytest.mark.asyncio
    async def test_database_initialization(self):
        """Test database initialization"""
        from app.core.database import test_connection
        
        # This will test if database connection works
        # In a real test environment, you'd use a test database
        try:
            result = await test_connection()
            # If we get here without exception, connection setup is working
            assert isinstance(result, bool)
        except Exception as e:
            # If database is not available, that's expected in CI/CD
            pytest.skip(f"Database not available: {e}")
    
    def test_error_handling(self, client):
        """Test API error handling"""
        # Test with invalid parameters
        response = client.get("/api/v1/ohlcv/data?symbol=&timeframe=")
        assert response.status_code in [400, 422]  # Bad request or validation error
        
        # Test with invalid exchange
        response = client.get("/api/v1/ohlcv/test-connection?exchange=invalid")
        assert response.status_code == 400
        
        data = response.json()
        assert "detail" in data
        assert "Unsupported exchange" in data["detail"]
