<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MACD Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e1e1e;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #444;
            border-radius: 5px;
            background-color: #2d2d2d;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #1a4d1a;
            border: 1px solid #2d7d2d;
        }
        .error {
            background-color: #4d1a1a;
            border: 1px solid #7d2d2d;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0052a3;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>MACD Calculation Fix Test</h1>
    
    <div class="test-section">
        <h2>Test 1: MACD Calculation with Sample Data</h2>
        <button onclick="testMACDCalculation()">Run MACD Test</button>
        <div id="macd-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Sparse Array Handling</h2>
        <button onclick="testSparseArrays()">Test Sparse Arrays</button>
        <div id="sparse-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Data Extraction</h2>
        <button onclick="testDataExtraction()">Test Data Extraction</button>
        <div id="extraction-test-result"></div>
    </div>

    <script>
        // Mock IndicatorsManager class for testing
        class TestIndicatorsManager {
            calculateEMA(data, period) {
                const ema = [];
                const multiplier = 2 / (period + 1);

                if (!data || data.length === 0) {
                    return ema;
                }

                // First EMA value is the SMA
                let sum = 0;
                for (let i = 0; i < Math.min(period, data.length); i++) {
                    sum += data[i].close;
                }
                ema[period - 1] = sum / period;

                // Calculate EMA for remaining values
                for (let i = period; i < data.length; i++) {
                    ema[i] = (data[i].close * multiplier) + (ema[i - 1] * (1 - multiplier));
                }

                return ema;
            }

            calculateMACD(data, fastPeriod, slowPeriod, signalPeriod) {
                const fastEMA = this.calculateEMA(data, fastPeriod);
                const slowEMA = this.calculateEMA(data, slowPeriod);

                const macdLine = [];
                const signalLine = [];
                const histogram = [];

                // Calculate MACD line (only for valid indices)
                for (let i = slowPeriod - 1; i < data.length; i++) {
                    if (fastEMA[i] !== undefined && slowEMA[i] !== undefined) {
                        macdLine[i] = fastEMA[i] - slowEMA[i];
                    }
                }

                // Create a clean array of MACD values for signal calculation
                const validMacdValues = [];
                for (let i = 0; i < macdLine.length; i++) {
                    if (macdLine[i] !== undefined) {
                        validMacdValues.push({ close: macdLine[i] });
                    }
                }

                // Calculate signal line (EMA of MACD line)
                if (validMacdValues.length > 0) {
                    const signal = this.calculateEMA(validMacdValues, signalPeriod);
                    
                    // Map signal values back to the correct indices
                    let signalIndex = 0;
                    for (let i = 0; i < macdLine.length; i++) {
                        if (macdLine[i] !== undefined) {
                            if (signal[signalIndex] !== undefined) {
                                signalLine[i] = signal[signalIndex];
                                histogram[i] = macdLine[i] - signal[signalIndex];
                            }
                            signalIndex++;
                        }
                    }
                }

                return { macd: macdLine, signal: signalLine, histogram: histogram };
            }
        }

        // Generate sample price data
        function generateSampleData(count = 50) {
            const data = [];
            let price = 50000;
            
            for (let i = 0; i < count; i++) {
                price += (Math.random() - 0.5) * 1000;
                data.push({
                    close: price,
                    timestamp: Date.now() + i * 900000 // 15 minutes apart
                });
            }
            
            return data;
        }

        function testMACDCalculation() {
            const resultDiv = document.getElementById('macd-test-result');
            const testManager = new TestIndicatorsManager();
            
            try {
                const sampleData = generateSampleData(50);
                const macdResult = testManager.calculateMACD(sampleData, 12, 26, 9);
                
                const macdCount = macdResult.macd.filter(v => v !== undefined).length;
                const signalCount = macdResult.signal.filter(v => v !== undefined).length;
                const histogramCount = macdResult.histogram.filter(v => v !== undefined).length;
                
                const latestMacd = macdResult.macd.filter(v => v !== undefined).pop();
                const latestSignal = macdResult.signal.filter(v => v !== undefined).pop();
                const latestHistogram = macdResult.histogram.filter(v => v !== undefined).pop();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ MACD Calculation Successful</h3>
                        <p><strong>Data points:</strong> ${sampleData.length}</p>
                        <p><strong>MACD values:</strong> ${macdCount}</p>
                        <p><strong>Signal values:</strong> ${signalCount}</p>
                        <p><strong>Histogram values:</strong> ${histogramCount}</p>
                        <p><strong>Latest MACD:</strong> ${latestMacd?.toFixed(4) || 'N/A'}</p>
                        <p><strong>Latest Signal:</strong> ${latestSignal?.toFixed(4) || 'N/A'}</p>
                        <p><strong>Latest Histogram:</strong> ${latestHistogram?.toFixed(4) || 'N/A'}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ MACD Calculation Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function testSparseArrays() {
            const resultDiv = document.getElementById('sparse-test-result');
            
            try {
                // Create a sparse array similar to what MACD calculation produces
                const sparseArray = [];
                sparseArray[25] = 10.5;
                sparseArray[26] = 11.2;
                sparseArray[27] = 9.8;
                sparseArray[30] = 12.1;
                
                // Test the extraction logic
                let latestValue = null;
                for (let i = sparseArray.length - 1; i >= 0; i--) {
                    if (sparseArray[i] !== undefined && sparseArray[i] !== null) {
                        latestValue = sparseArray[i];
                        break;
                    }
                }
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Sparse Array Handling Works</h3>
                        <p><strong>Array length:</strong> ${sparseArray.length}</p>
                        <p><strong>Defined values:</strong> ${sparseArray.filter(v => v !== undefined).length}</p>
                        <p><strong>Latest value:</strong> ${latestValue}</p>
                        <p><strong>Array structure:</strong></p>
                        <pre>${JSON.stringify(sparseArray, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Sparse Array Test Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function testDataExtraction() {
            const resultDiv = document.getElementById('extraction-test-result');
            
            try {
                // Simulate indicator data structure
                const mockIndicatorData = {
                    macd: {
                        macd: [undefined, undefined, undefined, 10.5, 11.2, 9.8, undefined, 12.1],
                        signal: [undefined, undefined, undefined, undefined, 8.5, 9.1, undefined, 10.2],
                        histogram: [undefined, undefined, undefined, undefined, 2.7, 0.7, undefined, 1.9]
                    }
                };
                
                // Test the extraction logic from marking-tools.js
                const macdData = {};
                
                // Find the latest valid MACD value
                if (mockIndicatorData.macd.macd && mockIndicatorData.macd.macd.length > 0) {
                    for (let i = mockIndicatorData.macd.macd.length - 1; i >= 0; i--) {
                        if (mockIndicatorData.macd.macd[i] !== undefined && mockIndicatorData.macd.macd[i] !== null) {
                            macdData.macd = mockIndicatorData.macd.macd[i];
                            break;
                        }
                    }
                }
                
                // Find the latest valid Signal value
                if (mockIndicatorData.macd.signal && mockIndicatorData.macd.signal.length > 0) {
                    for (let i = mockIndicatorData.macd.signal.length - 1; i >= 0; i--) {
                        if (mockIndicatorData.macd.signal[i] !== undefined && mockIndicatorData.macd.signal[i] !== null) {
                            macdData.signal = mockIndicatorData.macd.signal[i];
                            break;
                        }
                    }
                }
                
                // Find the latest valid Histogram value
                if (mockIndicatorData.macd.histogram && mockIndicatorData.macd.histogram.length > 0) {
                    for (let i = mockIndicatorData.macd.histogram.length - 1; i >= 0; i--) {
                        if (mockIndicatorData.macd.histogram[i] !== undefined && mockIndicatorData.macd.histogram[i] !== null) {
                            macdData.histogram = mockIndicatorData.macd.histogram[i];
                            break;
                        }
                    }
                }
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Data Extraction Works</h3>
                        <p><strong>Extracted MACD:</strong> ${macdData.macd || 'N/A'}</p>
                        <p><strong>Extracted Signal:</strong> ${macdData.signal || 'N/A'}</p>
                        <p><strong>Extracted Histogram:</strong> ${macdData.histogram || 'N/A'}</p>
                        <p><strong>Complete extracted data:</strong></p>
                        <pre>${JSON.stringify(macdData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Data Extraction Test Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
