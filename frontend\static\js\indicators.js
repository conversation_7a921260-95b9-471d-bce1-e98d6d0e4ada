// Enhanced Technical Indicators Management with Plot Functionality

class IndicatorsManager {
    constructor() {
        this.currentConfig = {};
        this.currentData = null;
        this.indicatorData = {};
        this.indicatorSeries = {};

        this.initEventListeners();
        this.setDefaultTimeframe();
    }

    initEventListeners() {
        document.getElementById('plotIndicators').addEventListener('click', () => {
            this.plotIndicators();
        });

        // Update config when checkboxes change
        const checkboxes = document.querySelectorAll('input[type="checkbox"][id$="-enabled"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateConfig();
            });
        });

        // Update config when parameters change
        const paramInputs = document.querySelectorAll('.indicator-group input[type="number"], .indicator-group input[type="color"], .indicator-group input[type="range"]');
        paramInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateConfig();
            });
        });

        // Special handler for opacity slider
        const opacitySlider = document.getElementById('bollinger-opacity');
        const opacityValue = document.getElementById('opacity-value');
        if (opacitySlider && opacityValue) {
            opacitySlider.addEventListener('input', () => {
                opacityValue.textContent = opacitySlider.value + '%';
            });
        }
    }

    setDefaultTimeframe() {
        // Set default timeframe to 15 minutes
        const timeframeSelect = document.getElementById('timeframe');
        if (timeframeSelect && timeframeSelect.value !== '15m') {
            timeframeSelect.value = '15m';
        }
    }

    safeParseFloat(value, context) {
        if (value === null || value === undefined) {
            console.warn(`Null/undefined value for ${context}`);
            return null;
        }

        // Handle different types
        if (typeof value === 'number') {
            return isNaN(value) ? null : value;
        }

        if (typeof value === 'string') {
            const parsed = parseFloat(value);
            if (isNaN(parsed)) {
                console.warn(`Cannot parse "${value}" as float for ${context}`);
                return null;
            }
            return parsed;
        }

        console.warn(`Unexpected value type ${typeof value} for ${context}:`, value);
        return null;
    }
    
    updateConfig() {
        this.currentConfig = {};

        // EMA with multiple periods (50, 100, 200)
        if (document.getElementById('ema-enabled').checked) {
            this.currentConfig.ema = {
                periods: [
                    parseInt(document.getElementById('ema-period1').value) || 50,
                    parseInt(document.getElementById('ema-period2').value) || 100,
                    parseInt(document.getElementById('ema-period3').value) || 200
                ],
                colors: [
                    document.getElementById('ema-color1').value || '#FF6B6B',
                    document.getElementById('ema-color2').value || '#4ECDC4',
                    document.getElementById('ema-color3').value || '#45B7D1'
                ]
            };
        }

        // Bollinger Bands (20, 2)
        if (document.getElementById('bollinger-enabled').checked) {
            this.currentConfig.bollinger_bands = {
                period: parseInt(document.getElementById('bollinger-period').value) || 20,
                std: parseFloat(document.getElementById('bollinger-std').value) || 2.0,
                lineColor: document.getElementById('bollinger-line-color').value || '#9C27B0',
                fillColor: document.getElementById('bollinger-fill-color').value || '#9C27B0',
                opacity: parseInt(document.getElementById('bollinger-opacity').value) || 20
            };
        }

        // MACD (12, 26, 9)
        if (document.getElementById('macd-enabled').checked) {
            this.currentConfig.macd = {
                fast: parseInt(document.getElementById('macd-fast').value) || 12,
                slow: parseInt(document.getElementById('macd-slow').value) || 26,
                signal: parseInt(document.getElementById('macd-signal').value) || 9,
                lineColor: document.getElementById('macd-line-color').value || '#2196F3',
                signalColor: document.getElementById('macd-signal-color').value || '#FF9800',
                histogramColor: document.getElementById('macd-histogram-color').value || '#4CAF50'
            };
        }

        // RSI with multiple periods (6, 12, 24)
        if (document.getElementById('rsi-enabled').checked) {
            this.currentConfig.rsi = {
                periods: [
                    parseInt(document.getElementById('rsi-period1').value) || 6,
                    parseInt(document.getElementById('rsi-period2').value) || 12,
                    parseInt(document.getElementById('rsi-period3').value) || 24
                ],
                colors: [
                    document.getElementById('rsi-color1').value || '#E91E63',
                    document.getElementById('rsi-color2').value || '#9C27B0',
                    document.getElementById('rsi-color3').value || '#673AB7'
                ]
            };
        }
    }
    
    async plotIndicators() {
        // Check if we have a strategy selected
        if (!window.strategyManager || !window.strategyManager.getCurrentStrategy()) {
            this.showError('Please select a strategy first.');
            return;
        }

        // Check if we have chart data
        if (!window.professionalChart || !window.professionalChart.candlestickSeries) {
            this.showError('No chart data available. Please load data first.');
            return;
        }

        this.updateConfig();

        if (Object.keys(this.currentConfig).length === 0) {
            this.showError('No indicators selected. Please enable at least one indicator.');
            return;
        }

        try {
            this.showLoading('Computing and plotting indicators...');

            // Get current chart data
            const chartData = this.getCurrentChartData();
            console.log('Chart data for indicators:', chartData.length, 'candles');

            if (!chartData || chartData.length < 50) {
                this.showError(`Insufficient data for indicator calculation. Have ${chartData.length} candles, need at least 50.`);
                return;
            }

            // Calculate indicators locally
            const indicators = this.calculateIndicatorsLocally(chartData);
            console.log('Calculated indicators:', Object.keys(indicators));

            // Plot indicators on chart
            this.plotIndicatorsOnChart(indicators);

            // Store indicator data for hover functionality
            this.indicatorData = indicators;

            // Setup hover functionality
            this.setupHoverTooltip();

            // Maintain chart configuration after plotting indicators
            this.maintainChartConfiguration();

            // Refresh chart markers after plotting indicators
            if (window.markingTools && window.markingTools.refreshChartMarkers) {
                window.markingTools.refreshChartMarkers();
            }

            this.showSuccess(`Indicators plotted successfully! Hover over candles to see values.`);

        } catch (error) {
            console.error('Error plotting indicators:', error);
            this.showError(`Error plotting indicators: ${error.message}`);
        }
    }
    
    getCurrentChartData() {
        // Get data from the stored current data
        if (!this.currentData || this.currentData.length === 0) {
            console.log('No current data available');
            return [];
        }

        console.log('=== CURRENT DATA ANALYSIS ===');
        console.log('Total items:', this.currentData.length);

        // Find first non-null item for analysis
        let sampleItem = null;
        for (let i = 0; i < Math.min(10, this.currentData.length); i++) {
            if (this.currentData[i] && typeof this.currentData[i] === 'object') {
                sampleItem = this.currentData[i];
                console.log(`Using item at index ${i} as sample:`, sampleItem);
                break;
            } else {
                console.warn(`Item at index ${i} is invalid:`, this.currentData[i]);
            }
        }

        if (!sampleItem) {
            console.error('No valid sample item found in first 10 items!');
            console.error('Raw data sample:', this.currentData.slice(0, 5));
            return [];
        }

        console.log('Available properties:', Object.keys(sampleItem));
        console.log('Data type:', typeof sampleItem);
        console.log('=== STARTING CONVERSION ===');

        // Convert the data format to what indicators expect
        const convertedData = this.currentData.map((item, index) => {
            // Handle different possible data formats
            let open, high, low, close, volume;

            // Debug the first few items to understand the format
            if (index < 3) {
                console.log(`=== ITEM ${index} ANALYSIS ===`);
                console.log('Full item:', item);
                console.log('Properties available:', Object.keys(item));
                console.log('Values:');
                Object.keys(item).forEach(key => {
                    console.log(`  ${key}:`, item[key], `(${typeof item[key]})`);
                });
                console.log('=== END ITEM ANALYSIS ===');
            }

            // Try different property names and handle string/number conversion
            const props = Object.keys(item);

            // Method 1: Standard OHLCV properties
            if (props.includes('open') && item.open !== null && item.open !== undefined) {
                open = item.open;
                high = item.high;
                low = item.low;
                close = item.close;
                volume = item.volume;
                if (index < 3) console.log(`Using standard format: open=${open}, close=${close}`);
            }
            // Method 2: Alternative naming with _price suffix
            else if (props.includes('open_price')) {
                open = item.open_price;
                high = item.high_price;
                low = item.low_price;
                close = item.close_price;
                volume = item.volume;
                if (index < 3) console.log(`Using _price format: open_price=${open}, close_price=${close}`);
            }
            // Method 3: Try to find any price-related properties
            else {
                // Look for any properties that might contain price data
                const openProps = props.filter(p => p.toLowerCase().includes('open'));
                const closeProps = props.filter(p => p.toLowerCase().includes('close'));
                const highProps = props.filter(p => p.toLowerCase().includes('high'));
                const lowProps = props.filter(p => p.toLowerCase().includes('low'));

                if (openProps.length > 0 && closeProps.length > 0) {
                    open = item[openProps[0]];
                    close = item[closeProps[0]];
                    high = highProps.length > 0 ? item[highProps[0]] : open;
                    low = lowProps.length > 0 ? item[lowProps[0]] : open;
                    volume = item.volume || 0;
                    if (index < 3) console.log(`Using detected format: ${openProps[0]}=${open}, ${closeProps[0]}=${close}`);
                } else {
                    console.error(`Cannot find price data at index ${index}:`, item);
                    console.error('Available properties:', props);
                    console.error('Looking for properties containing: open, close, high, low');
                    return null;
                }
            }

            // Convert to numbers, handling various input types
            const converted = {
                timestamp: item.timestamp,
                open: this.safeParseFloat(open, `open at index ${index}`),
                high: this.safeParseFloat(high, `high at index ${index}`),
                low: this.safeParseFloat(low, `low at index ${index}`),
                close: this.safeParseFloat(close, `close at index ${index}`),
                volume: this.safeParseFloat(volume || 0, `volume at index ${index}`)
            };

            // Validate the converted data
            if (converted.open === null || converted.high === null || converted.low === null || converted.close === null) {
                console.error(`Failed to convert numeric values at index ${index}:`, {
                    original: item,
                    extracted: { open, high, low, close, volume },
                    converted: converted
                });
                return null;
            }

            return converted;
        }).filter(item => {
            // More robust filtering
            if (!item || item === null || item === undefined) {
                return false;
            }

            // Ensure all required numeric fields are valid
            const requiredFields = ['open', 'high', 'low', 'close'];
            for (const field of requiredFields) {
                if (typeof item[field] !== 'number' || isNaN(item[field]) || item[field] === null || item[field] === undefined) {
                    console.warn(`Invalid ${field} value in item:`, item);
                    return false;
                }
            }

            return true;
        });

        console.log('=== CONVERSION COMPLETE ===');
        console.log('Original items:', this.currentData.length);
        console.log('Successfully converted:', convertedData.length);
        console.log('Failed conversions:', this.currentData.length - convertedData.length);

        if (convertedData.length > 0) {
            console.log('Sample converted data:', convertedData[0]);
            console.log('Converted data properties:', Object.keys(convertedData[0]));
            console.log('Sample values:', {
                open: convertedData[0].open,
                high: convertedData[0].high,
                low: convertedData[0].low,
                close: convertedData[0].close,
                volume: convertedData[0].volume
            });
        } else {
            console.error('NO DATA WAS SUCCESSFULLY CONVERTED!');
            console.error('Check the data format and conversion logic');
        }
        console.log('=== END CONVERSION ===');

        return convertedData;
    }

    calculateIndicatorsLocally(data) {
        const indicators = {};

        console.log('Calculating indicators with data:', data.length, 'candles');
        console.log('Current config:', this.currentConfig);

        if (!data || data.length === 0) {
            console.error('No data provided for indicator calculations');
            return indicators;
        }

        // Final validation before calculations
        console.log('=== FINAL DATA VALIDATION ===');
        console.log('Data array length:', data.length);
        console.log('First item:', data[0]);
        console.log('First item type:', typeof data[0]);

        if (data[0]) {
            console.log('First item properties:', Object.keys(data[0]));
            console.log('First item close value:', data[0].close, 'type:', typeof data[0].close);
        }

        // Validate first few items
        const validItems = data.slice(0, 5).filter(item =>
            item &&
            typeof item === 'object' &&
            typeof item.close === 'number' &&
            !isNaN(item.close)
        );

        console.log(`Valid items in first 5: ${validItems.length}/5`);

        if (validItems.length === 0) {
            console.error('No valid items found in data! Cannot calculate indicators.');
            console.error('Sample invalid items:', data.slice(0, 3));
            return indicators;
        }

        console.log('=== END FINAL VALIDATION ===');

        try {
            // Calculate EMA for multiple periods
            if (this.currentConfig.ema) {
                indicators.ema = {};
                this.currentConfig.ema.periods.forEach(period => {
                    console.log(`Calculating EMA ${period} with ${data.length} data points`);
                    indicators.ema[`ema_${period}`] = this.calculateEMA(data, period);
                });
            }

            // Calculate Bollinger Bands
            if (this.currentConfig.bollinger_bands) {
                console.log('Calculating Bollinger Bands');
                const bb = this.calculateBollingerBands(data,
                    this.currentConfig.bollinger_bands.period,
                    this.currentConfig.bollinger_bands.std);
                indicators.bollinger_bands = bb;
            }

            // Calculate MACD
            if (this.currentConfig.macd) {
                console.log('Calculating MACD');
                indicators.macd = this.calculateMACD(data,
                    this.currentConfig.macd.fast,
                    this.currentConfig.macd.slow,
                    this.currentConfig.macd.signal);
            }

            // Calculate RSI for multiple periods
            if (this.currentConfig.rsi) {
                indicators.rsi = {};
                this.currentConfig.rsi.periods.forEach(period => {
                    console.log(`Calculating RSI ${period}`);
                    indicators.rsi[`rsi_${period}`] = this.calculateRSI(data, period);
                });
            }
        } catch (error) {
            console.error('Error in calculateIndicatorsLocally:', error);
            throw error;
        }

        return indicators;
    }
    
    calculateEMA(data, period) {
        const ema = [];
        const multiplier = 2 / (period + 1);

        if (!data || data.length === 0) {
            console.error('No data provided for EMA calculation');
            return ema;
        }

        // Comprehensive data validation
        if (!data || data.length === 0) {
            console.error('EMA calculation: No data provided');
            return ema;
        }

        if (!data[0] || typeof data[0].close === 'undefined' || data[0].close === null) {
            console.error('Invalid data format for EMA calculation. First item:', data[0]);
            console.error('Expected format: {close: number, ...}');
            console.error('Data sample:', data.slice(0, 3));
            return ema;
        }

        // Additional validation for sufficient data
        if (data.length < period) {
            console.warn(`Insufficient data for EMA ${period}. Have ${data.length}, need ${period}`);
            return ema;
        }

        // Validate that all data points have valid close values
        const invalidItems = data.filter(item => !item || typeof item.close !== 'number' || isNaN(item.close));
        if (invalidItems.length > 0) {
            console.error(`Found ${invalidItems.length} invalid data items for EMA calculation:`, invalidItems.slice(0, 3));
            return ema;
        }

        // First EMA value is the SMA
        let sum = 0;
        for (let i = 0; i < Math.min(period, data.length); i++) {
            if (typeof data[i].close !== 'number' || isNaN(data[i].close)) {
                console.error(`Invalid close price at index ${i}:`, data[i]);
                return ema;
            }
            sum += data[i].close;
        }
        ema[period - 1] = sum / period;

        // Calculate EMA for remaining values
        for (let i = period; i < data.length; i++) {
            if (typeof data[i].close !== 'number' || isNaN(data[i].close)) {
                console.error(`Invalid close price at index ${i}:`, data[i]);
                break;
            }
            ema[i] = (data[i].close * multiplier) + (ema[i - 1] * (1 - multiplier));
        }

        console.log(`EMA ${period} calculated for ${ema.filter(v => v !== undefined).length} points`);
        return ema;
    }

    calculateBollingerBands(data, period, stdDev) {
        const bb = { upper: [], middle: [], lower: [] };

        for (let i = period - 1; i < data.length; i++) {
            // Calculate SMA (middle band)
            let sum = 0;
            for (let j = i - period + 1; j <= i; j++) {
                sum += data[j].close;
            }
            const sma = sum / period;
            bb.middle[i] = sma;

            // Calculate standard deviation
            let variance = 0;
            for (let j = i - period + 1; j <= i; j++) {
                variance += Math.pow(data[j].close - sma, 2);
            }
            const std = Math.sqrt(variance / period);

            // Calculate upper and lower bands
            bb.upper[i] = sma + (stdDev * std);
            bb.lower[i] = sma - (stdDev * std);
        }

        return bb;
    }

    calculateMACD(data, fastPeriod, slowPeriod, signalPeriod) {
        const fastEMA = this.calculateEMA(data, fastPeriod);
        const slowEMA = this.calculateEMA(data, slowPeriod);

        const macdLine = [];
        const signalLine = [];
        const histogram = [];

        // Calculate MACD line (only for valid indices)
        for (let i = slowPeriod - 1; i < data.length; i++) {
            if (fastEMA[i] !== undefined && slowEMA[i] !== undefined) {
                macdLine[i] = fastEMA[i] - slowEMA[i];
            }
        }

        // Create a clean array of MACD values for signal calculation
        const validMacdValues = [];
        for (let i = 0; i < macdLine.length; i++) {
            if (macdLine[i] !== undefined) {
                validMacdValues.push({ close: macdLine[i] });
            }
        }

        // Calculate signal line (EMA of MACD line)
        if (validMacdValues.length > 0) {
            const signal = this.calculateEMA(validMacdValues, signalPeriod);

            // Map signal values back to the correct indices
            let signalIndex = 0;
            for (let i = 0; i < macdLine.length; i++) {
                if (macdLine[i] !== undefined) {
                    if (signal[signalIndex] !== undefined) {
                        signalLine[i] = signal[signalIndex];
                        histogram[i] = macdLine[i] - signal[signalIndex];
                    }
                    signalIndex++;
                }
            }
        }

        console.log(`MACD calculation completed: ${macdLine.filter(v => v !== undefined).length} MACD values, ${signalLine.filter(v => v !== undefined).length} signal values`);

        return { macd: macdLine, signal: signalLine, histogram: histogram };
    }

    calculateRSI(data, period) {
        const rsi = [];
        const gains = [];
        const losses = [];

        // Calculate price changes
        for (let i = 1; i < data.length; i++) {
            const change = data[i].close - data[i - 1].close;
            gains[i] = change > 0 ? change : 0;
            losses[i] = change < 0 ? -change : 0;
        }

        // Calculate RSI
        for (let i = period; i < data.length; i++) {
            let avgGain = 0;
            let avgLoss = 0;

            // Calculate average gain and loss
            for (let j = i - period + 1; j <= i; j++) {
                avgGain += gains[j] || 0;
                avgLoss += losses[j] || 0;
            }

            avgGain /= period;
            avgLoss /= period;

            if (avgLoss === 0) {
                rsi[i] = 100;
            } else {
                const rs = avgGain / avgLoss;
                rsi[i] = 100 - (100 / (1 + rs));
            }
        }

        return rsi;
    }
    
    plotIndicatorsOnChart(indicators) {
        if (!window.professionalChart || !window.professionalChart.chart) {
            console.error('Chart not available for plotting indicators');
            return;
        }

        const chart = window.professionalChart.chart;

        // Store current price scale configuration before plotting
        const currentPriceScaleOptions = {
            autoScale: true,
            mode: 0,
            tickMarkFormatter: (price) => {
                const interval = 250;
                const rounded = Math.round(price / interval) * interval;
                if (Math.abs(price - rounded) < 1) {
                    if (rounded >= 1000) {
                        return `$${(rounded / 1000).toFixed(1)}K`;
                    } else {
                        return `$${rounded.toFixed(0)}`;
                    }
                }
                return '';
            }
        };

        // Temporarily disable autoscaling to prevent price scale changes during indicator plotting
        chart.applyOptions({
            rightPriceScale: {
                autoScale: false // Disable autoscaling temporarily
            }
        });

        console.log('🔒 Autoscaling disabled during indicator plotting');

        // Clear existing indicator series
        this.clearIndicatorSeries();

        // Plot EMA lines
        if (indicators.ema) {
            // Get theme-appropriate default colors if no custom colors set
            const themeColors = window.chartThemeManager ?
                window.chartThemeManager.getIndicatorColors().ema :
                ['#FF6B6B', '#4ECDC4', '#45B7D1'];

            const colors = this.currentConfig.ema?.colors || themeColors;
            let colorIndex = 0;

            Object.keys(indicators.ema).forEach(emaKey => {
                const emaData = indicators.ema[emaKey];
                const period = emaKey.split('_')[1];

                const lineSeries = chart.addLineSeries({
                    color: colors[colorIndex % colors.length],
                    lineWidth: 2,
                    title: `EMA ${period}`,
                    lastValueVisible: true,
                    priceLineVisible: false,
                    priceScaleId: 'right' // Use same scale as candlesticks
                });

                // Convert EMA data to chart format
                const chartData = this.currentData.map((candle, index) => {
                    const timestamp = typeof candle.timestamp === 'string'
                        ? new Date(candle.timestamp).getTime() / 1000
                        : candle.timestamp / 1000;
                    return {
                        time: Math.floor(timestamp),
                        value: emaData[index] || null
                    };
                }).filter(point => point.value !== null);

                lineSeries.setData(chartData);
                this.indicatorSeries[emaKey] = lineSeries;
                colorIndex++;
            });
        }

        // Plot Bollinger Bands
        if (indicators.bollinger_bands) {
            const bb = indicators.bollinger_bands;
            const config = this.currentConfig.bollinger_bands || {};
            const lineColor = config.lineColor || '#9C27B0';
            const fillColor = config.fillColor || '#9C27B0';
            const opacity = (config.opacity || 20) / 100;

            // Upper band
            const upperSeries = chart.addLineSeries({
                color: lineColor,
                lineWidth: 1,
                title: 'BB Upper',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right' // Use same scale as candlesticks
            });

            // Middle band (SMA)
            const middleSeries = chart.addLineSeries({
                color: lineColor,
                lineWidth: 2,
                title: 'BB Middle',
                lastValueVisible: true,
                priceLineVisible: false,
                priceScaleId: 'right' // Use same scale as candlesticks
            });

            // Lower band
            const lowerSeries = chart.addLineSeries({
                color: lineColor,
                lineWidth: 1,
                title: 'BB Lower',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right' // Use same scale as candlesticks
            });

            // Create fill area using area series instead of histogram
            // This approach won't interfere with price scaling
            const fillAreaSeries = chart.addAreaSeries({
                topColor: `${fillColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
                bottomColor: `${fillColor}00`, // Fully transparent bottom
                lineColor: 'transparent',
                lineWidth: 0,
                title: 'BB Fill',
                lastValueVisible: false,
                priceLineVisible: false,
                priceScaleId: 'right' // Use same scale as candlesticks
            });

            // Convert data to chart format
            const upperData = this.currentData.map((candle, index) => {
                const timestamp = typeof candle.timestamp === 'string'
                    ? new Date(candle.timestamp).getTime() / 1000
                    : candle.timestamp / 1000;
                return {
                    time: Math.floor(timestamp),
                    value: bb.upper[index] || null
                };
            }).filter(point => point.value !== null);

            const middleData = this.currentData.map((candle, index) => {
                const timestamp = typeof candle.timestamp === 'string'
                    ? new Date(candle.timestamp).getTime() / 1000
                    : candle.timestamp / 1000;
                return {
                    time: Math.floor(timestamp),
                    value: bb.middle[index] || null
                };
            }).filter(point => point.value !== null);

            const lowerData = this.currentData.map((candle, index) => {
                const timestamp = typeof candle.timestamp === 'string'
                    ? new Date(candle.timestamp).getTime() / 1000
                    : candle.timestamp / 1000;
                return {
                    time: Math.floor(timestamp),
                    value: bb.lower[index] || null
                };
            }).filter(point => point.value !== null);

            // Create fill area data using upper band values
            // Area series will fill from the line down to the baseline (which we'll set to lower band)
            const fillData = this.currentData.map((candle, index) => {
                const timestamp = typeof candle.timestamp === 'string'
                    ? new Date(candle.timestamp).getTime() / 1000
                    : candle.timestamp / 1000;

                if (bb.upper[index] !== undefined && bb.lower[index] !== undefined) {
                    // Use upper band value for area series
                    // The area will be filled between upper band and the baseline
                    return {
                        time: Math.floor(timestamp),
                        value: bb.upper[index]
                    };
                }
                return null;
            }).filter(point => point !== null);

            // Set the data for all series
            upperSeries.setData(upperData);
            middleSeries.setData(middleData);
            lowerSeries.setData(lowerData);
            fillAreaSeries.setData(fillData);

            // Store series references
            this.indicatorSeries.bb_fill = fillAreaSeries;
            this.indicatorSeries.bb_upper = upperSeries;
            this.indicatorSeries.bb_middle = middleSeries;
            this.indicatorSeries.bb_lower = lowerSeries;
        }

        // Ensure all indicator series are properly configured to not interfere with price scaling
        Object.values(this.indicatorSeries).forEach(series => {
            if (series && series.applyOptions) {
                series.applyOptions({
                    priceScaleId: 'right', // Ensure all indicators use the same price scale
                    lastValueVisible: series.options?.lastValueVisible !== undefined ? series.options.lastValueVisible : false,
                    priceLineVisible: false // Disable price lines to avoid scale interference
                });
            }
        });

        // Re-enable autoscaling and restore custom price scale configuration
        chart.applyOptions({
            rightPriceScale: currentPriceScaleOptions
        });

        // Maintain position at earliest data after indicator plotting
        this.maintainEarliestDataPosition();

        console.log('🔓 Autoscaling re-enabled with custom price scale configuration');
        console.log('Indicators plotted on chart:', Object.keys(this.indicatorSeries));
        console.log('Price scale protection applied to all indicator series');
    }

    clearIndicatorSeries() {
        // Remove existing indicator series from chart
        Object.values(this.indicatorSeries).forEach(series => {
            try {
                window.professionalChart.chart.removeSeries(series);
            } catch (error) {
                console.warn('Error removing series:', error);
            }
        });
        this.indicatorSeries = {};
    }

    setupHoverTooltip() {
        if (!window.professionalChart || !window.professionalChart.chart) {
            return;
        }

        const chart = window.professionalChart.chart;

        // Subscribe to crosshair move events
        chart.subscribeCrosshairMove((param) => {
            if (!param.time) {
                this.hideTooltip();
                return;
            }

            // Find the data index for this timestamp
            const timestamp = param.time * 1000; // Convert to milliseconds
            const dataIndex = this.findDataIndexByTimestamp(timestamp);

            if (dataIndex !== -1) {
                this.showIndicatorTooltip(dataIndex, param.point);
            }
        });
    }

    findDataIndexByTimestamp(timestamp) {
        if (!this.currentData) return -1;

        for (let i = 0; i < this.currentData.length; i++) {
            const candleTime = typeof this.currentData[i].timestamp === 'string'
                ? new Date(this.currentData[i].timestamp).getTime()
                : this.currentData[i].timestamp;
            if (Math.abs(candleTime - timestamp) < 60000) { // Within 1 minute
                return i;
            }
        }
        return -1;
    }



    showIndicatorTooltip(dataIndex, point) {
        if (!this.indicatorData || !point) return;

        let tooltipContent = '<div class="indicator-tooltip">';

        // Add EMA values
        if (this.indicatorData.ema) {
            tooltipContent += '<div class="tooltip-section"><strong>EMA:</strong>';
            Object.keys(this.indicatorData.ema).forEach(emaKey => {
                const period = emaKey.split('_')[1];
                const value = this.indicatorData.ema[emaKey][dataIndex];
                if (value !== undefined && value !== null) {
                    tooltipContent += `<br>EMA ${period}: ${value.toFixed(4)}`;
                }
            });
            tooltipContent += '</div>';
        }

        // Add Bollinger Bands values
        if (this.indicatorData.bollinger_bands) {
            const bb = this.indicatorData.bollinger_bands;
            tooltipContent += '<div class="tooltip-section"><strong>Bollinger Bands:</strong>';
            if (bb.upper[dataIndex] !== undefined) {
                tooltipContent += `<br>Upper: ${bb.upper[dataIndex].toFixed(4)}`;
                tooltipContent += `<br>Middle: ${bb.middle[dataIndex].toFixed(4)}`;
                tooltipContent += `<br>Lower: ${bb.lower[dataIndex].toFixed(4)}`;
            }
            tooltipContent += '</div>';
        }

        // Add MACD values
        if (this.indicatorData.macd) {
            const macd = this.indicatorData.macd;
            tooltipContent += '<div class="tooltip-section"><strong>MACD:</strong>';

            // Get MACD value at dataIndex (handle sparse arrays)
            const macdValue = macd.macd && macd.macd[dataIndex] !== undefined ? macd.macd[dataIndex] : null;
            const signalValue = macd.signal && macd.signal[dataIndex] !== undefined ? macd.signal[dataIndex] : null;
            const histogramValue = macd.histogram && macd.histogram[dataIndex] !== undefined ? macd.histogram[dataIndex] : null;

            if (macdValue !== null) {
                tooltipContent += `<br>MACD: ${macdValue.toFixed(4)}`;
            } else {
                tooltipContent += `<br>MACD: N/A`;
            }

            if (signalValue !== null) {
                tooltipContent += `<br>Signal: ${signalValue.toFixed(4)}`;
            } else {
                tooltipContent += `<br>Signal: N/A`;
            }

            if (histogramValue !== null) {
                tooltipContent += `<br>Histogram: ${histogramValue.toFixed(4)}`;
            } else {
                tooltipContent += `<br>Histogram: N/A`;
            }

            tooltipContent += '</div>';
        }

        // Add RSI values
        if (this.indicatorData.rsi) {
            tooltipContent += '<div class="tooltip-section"><strong>RSI:</strong>';
            Object.keys(this.indicatorData.rsi).forEach(rsiKey => {
                const period = rsiKey.split('_')[1];
                const value = this.indicatorData.rsi[rsiKey][dataIndex];
                if (value !== undefined && value !== null) {
                    tooltipContent += `<br>RSI ${period}: ${value.toFixed(2)}`;
                }
            });
            tooltipContent += '</div>';
        }

        tooltipContent += '</div>';

        // Show tooltip
        this.displayTooltip(tooltipContent, point);
    }

    displayTooltip(content, point) {
        let tooltip = document.getElementById('indicator-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'indicator-tooltip';
            tooltip.className = 'indicator-tooltip-popup';
            document.body.appendChild(tooltip);
        }

        tooltip.innerHTML = content;
        tooltip.style.display = 'block';
        tooltip.style.left = (point.x + 10) + 'px';
        tooltip.style.top = (point.y - 10) + 'px';
    }

    hideTooltip() {
        const tooltip = document.getElementById('indicator-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    setCurrentData(data) {
        // Clean and validate the data array before storing
        if (!data || !Array.isArray(data)) {
            console.error('Invalid data provided to setCurrentData:', data);
            this.currentData = [];
            return;
        }

        // Filter out any null, undefined, or invalid items
        const cleanData = data.filter((item, index) => {
            if (!item || typeof item !== 'object') {
                console.warn(`Removing invalid item at index ${index}:`, item);
                return false;
            }
            return true;
        });

        this.currentData = cleanData;
        console.log('Indicators Manager received data:', data.length, 'original,', cleanData.length, 'clean candles');

        if (cleanData.length > 0) {
            console.log('Sample data format:', cleanData[0]);
            console.log('Data properties:', Object.keys(cleanData[0]));

            // Detailed analysis of the first item
            const firstItem = cleanData[0];
            console.log('Detailed first item analysis:');
            console.log('- open:', firstItem.open, '(type:', typeof firstItem.open, ')');
            console.log('- high:', firstItem.high, '(type:', typeof firstItem.high, ')');
            console.log('- low:', firstItem.low, '(type:', typeof firstItem.low, ')');
            console.log('- close:', firstItem.close, '(type:', typeof firstItem.close, ')');
            console.log('- volume:', firstItem.volume, '(type:', typeof firstItem.volume, ')');

            // Test data conversion immediately
            const testConverted = this.getCurrentChartData();
            console.log('Test conversion result:', testConverted.length, 'items');
            if (testConverted.length > 0) {
                console.log('Sample converted data:', testConverted[0]);
            } else {
                console.error('Data conversion failed - no items converted');
            }
        } else {
            console.error('No valid data items after cleaning!');
        }
    }

    maintainEarliestDataPosition() {
        // Maintain chart position at earliest data after indicator plotting
        if (window.professionalChart && window.professionalChart.positionToEarliestData && this.currentData) {
            // Convert current data to chart format for positioning
            const chartData = this.currentData.map(candle => ({
                time: Math.floor(new Date(candle.timestamp).getTime() / 1000),
                open: parseFloat(candle.open),
                high: parseFloat(candle.high),
                low: parseFloat(candle.low),
                close: parseFloat(candle.close)
            }));

            // Position to earliest data
            window.professionalChart.positionToEarliestData(chartData);
            console.log('📍 Chart repositioned to earliest data after indicator plotting');
        } else if (window.chartManager && window.chartManager.positionToEarliestData && this.currentData) {
            // Fallback to chartManager if available
            const chartData = this.currentData.map(candle => ({
                time: new Date(candle.timestamp).getTime() / 1000,
                open: parseFloat(candle.open),
                high: parseFloat(candle.high),
                low: parseFloat(candle.low),
                close: parseFloat(candle.close)
            }));

            window.chartManager.positionToEarliestData(chartData);
            console.log('📍 Chart repositioned to earliest data after indicator plotting (via chartManager)');
        }
    }

    maintainChartConfiguration() {
        // Chart configuration is now maintained directly in plotIndicatorsOnChart
        // This method is kept for compatibility but the main work is done during plotting
        console.log('Chart configuration maintained during indicator plotting process');

        // Apply time scale configuration if needed
        if (window.professionalChart && window.professionalChart.applyChartConfiguration) {
            window.professionalChart.applyChartConfiguration();
        }
    }



    getCurrentConfig() {
        this.updateConfig();
        return this.currentConfig;
    }

    // Debug method for testing
    testIndicatorCalculation() {
        console.log('=== INDICATOR DEBUG TEST ===');
        console.log('Current data length:', this.currentData ? this.currentData.length : 'null');

        if (this.currentData && this.currentData.length > 0) {
            console.log('First 3 raw data items:', this.currentData.slice(0, 3));

            const chartData = this.getCurrentChartData();
            console.log('Converted data length:', chartData.length);
            console.log('First 3 converted items:', chartData.slice(0, 3));

            if (chartData.length > 50) {
                // Test EMA calculation with a small dataset
                const testData = chartData.slice(0, 100);
                console.log('Testing EMA calculation with', testData.length, 'items');

                try {
                    const ema20 = this.calculateEMA(testData, 20);
                    console.log('EMA 20 calculation successful, length:', ema20.length);
                    console.log('EMA 20 sample values:', ema20.slice(19, 25));
                } catch (error) {
                    console.error('EMA calculation failed:', error);
                }
            } else {
                console.log('Not enough data for EMA test (need >50, have', chartData.length, ')');
            }
        } else {
            console.log('No data available for testing');
        }
        console.log('=== END DEBUG TEST ===');
    }
    
    showLoading(message) {
        const statusElement = document.getElementById('indicator-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = 'status-message loading';
            statusElement.style.display = 'block';
        }
    }

    showSuccess(message) {
        const statusElement = document.getElementById('indicator-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = 'status-message success';
            statusElement.style.display = 'block';
        }

        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    }

    showError(message) {
        const statusElement = document.getElementById('indicator-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = 'status-message error';
            statusElement.style.display = 'block';
        }

        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 8000);
    }
}

// Global indicators manager instance
window.indicatorsManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.indicatorsManager = new IndicatorsManager();
    console.log('Enhanced Indicators Manager initialized with plot functionality');

    // Add global debug functions
    window.debugIndicators = () => {
        if (window.indicatorsManager) {
            window.indicatorsManager.testIndicatorCalculation();
        } else {
            console.log('Indicators manager not available');
        }
    };

    window.debugDataFormat = () => {
        if (window.indicatorsManager && window.indicatorsManager.currentData) {
            const data = window.indicatorsManager.currentData;
            console.log('=== DATA FORMAT DEBUG ===');
            console.log('Total items:', data.length);
            if (data.length > 0) {
                console.log('First item:', data[0]);
                console.log('Properties:', Object.keys(data[0]));
                console.log('Values:');
                Object.keys(data[0]).forEach(key => {
                    console.log(`  ${key}:`, data[0][key], `(${typeof data[0][key]})`);
                });
            }
            console.log('=== END DATA FORMAT DEBUG ===');
        } else {
            console.log('No data available for debugging');
        }
    };

    console.log('Debug functions available:');
    console.log('- window.debugIndicators() - Test indicator calculations');
    console.log('- window.debugDataFormat() - Analyze data format');
});
