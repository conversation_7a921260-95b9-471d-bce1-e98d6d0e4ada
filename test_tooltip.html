<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced OHLCV Tooltip Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .chart-container {
            width: 100%;
            height: 600px;
            border: 1px solid #363c4e;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid #363c4e;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .feature-item {
            padding: 10px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .feature-item h4 {
            margin: 0 0 5px 0;
            color: #26a69a;
        }
        
        .feature-item p {
            margin: 0;
            font-size: 14px;
            color: #d1d4dc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Enhanced OHLCV Tooltip</h1>
            <p>Hover over candlesticks to see detailed OHLCV data with enhanced formatting</p>
        </div>
        
        <div class="chart-container" id="chart"></div>
        
        <div class="info-panel">
            <h3>🚀 Enhanced Features</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>📅 Detailed Timestamp</h4>
                    <p>Shows full date/time with timezone and Unix timestamp for reference</p>
                </div>
                <div class="feature-item">
                    <h4>💰 Precise Pricing</h4>
                    <p>High-precision price formatting with up to 8 decimal places</p>
                </div>
                <div class="feature-item">
                    <h4>📈 Price Change</h4>
                    <p>Shows price change amount and percentage with color coding</p>
                </div>
                <div class="feature-item">
                    <h4>📊 Smart Volume</h4>
                    <p>Volume formatting with K/M suffixes for readability</p>
                </div>
                <div class="feature-item">
                    <h4>🎯 Current Price</h4>
                    <p>Real-time price at cursor position on the right edge</p>
                </div>
                <div class="feature-item">
                    <h4>🎨 Modern Design</h4>
                    <p>Glass-morphism design with backdrop blur and smooth animations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- TradingView Lightweight Charts -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    
    <script>
        // Create chart
        const chart = LightweightCharts.createChart(document.getElementById('chart'), {
            width: document.getElementById('chart').clientWidth,
            height: 600,
            layout: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
                fontSize: 12,
                fontFamily: 'Segoe UI, sans-serif',
            },
            grid: {
                vertLines: {
                    color: '#363c4e',
                    style: 1,
                    visible: true,
                },
                horzLines: {
                    color: '#363c4e',
                    style: 1,
                    visible: true,
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
                vertLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                },
                horzLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                },
            },
            priceScale: {
                borderColor: '#485c7b',
            },
            timeScale: {
                borderColor: '#485c7b',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        // Create candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // Create volume series
        const volumeSeries = chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });

        // Generate sample data
        const generateSampleData = () => {
            const data = [];
            const volumeData = [];
            let price = 50000;
            const startTime = Math.floor(Date.now() / 1000) - (100 * 15 * 60); // 100 candles, 15 minutes each
            
            for (let i = 0; i < 100; i++) {
                const time = startTime + (i * 15 * 60);
                const change = (Math.random() - 0.5) * 1000;
                const open = price;
                const close = price + change;
                const high = Math.max(open, close) + Math.random() * 200;
                const low = Math.min(open, close) - Math.random() * 200;
                const volume = Math.random() * 1000000 + 100000;
                
                data.push({
                    time: time,
                    open: open,
                    high: high,
                    low: low,
                    close: close
                });
                
                volumeData.push({
                    time: time,
                    value: volume,
                    color: close >= open ? '#26a69a80' : '#ef535080'
                });
                
                price = close;
            }
            
            return { candleData: data, volumeData: volumeData };
        };

        // Load sample data
        const sampleData = generateSampleData();
        candlestickSeries.setData(sampleData.candleData);
        volumeSeries.setData(sampleData.volumeData);

        // Create tooltip elements (similar to the enhanced version)
        const container = document.getElementById('chart');
        container.style.position = 'relative';

        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: absolute;
            background: linear-gradient(135deg, rgba(19, 23, 34, 0.95), rgba(19, 23, 34, 0.98));
            color: white;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 12px;
            line-height: 1.5;
            pointer-events: none;
            z-index: 1000;
            display: none;
            min-width: 280px;
            max-width: 320px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
        `;

        const priceDisplay = document.createElement('div');
        priceDisplay.style.cssText = `
            position: absolute;
            right: 8px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.9));
            color: #000;
            padding: 6px 10px;
            border-radius: 6px;
            font-family: 'SF Mono', monospace;
            font-size: 12px;
            font-weight: 600;
            pointer-events: none;
            z-index: 1001;
            display: none;
            border: 1px solid rgba(255, 215, 0, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        `;

        container.appendChild(tooltip);
        container.appendChild(priceDisplay);

        // Enhanced crosshair move handler
        chart.subscribeCrosshairMove(param => {
            if (!param.time || !param.seriesData) {
                tooltip.style.display = 'none';
                priceDisplay.style.display = 'none';
                return;
            }

            const candleData = param.seriesData.get(candlestickSeries);
            const volumeData = param.seriesData.get(volumeSeries);

            if (!candleData) {
                tooltip.style.display = 'none';
                return;
            }

            // Format data (similar to enhanced version)
            const date = new Date(param.time * 1000);
            const timestamp = date.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
                timeZoneName: 'short'
            });

            const formatPrice = (price) => {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(price);
            };

            const formatVolume = (volume) => {
                if (volume >= 1000000) {
                    return (volume / 1000000).toFixed(2) + 'M';
                } else if (volume >= 1000) {
                    return (volume / 1000).toFixed(2) + 'K';
                }
                return volume.toFixed(0);
            };

            const priceChange = candleData.close - candleData.open;
            const priceChangePercent = ((priceChange / candleData.open) * 100);
            const changeColor = priceChange >= 0 ? '#26a69a' : '#ef5350';
            const changeSymbol = priceChange >= 0 ? '+' : '';

            const currentPrice = chart.coordinateToPrice(param.point?.y || 0);

            tooltip.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 10px; color: #26a69a; font-size: 13px; border-bottom: 1px solid #444; padding-bottom: 5px;">
                    📊 OHLCV Data
                </div>
                <div style="margin-bottom: 8px;">
                    <div style="font-size: 11px; color: #888; margin-bottom: 4px;">
                        <strong>Timestamp:</strong> ${timestamp}
                    </div>
                    <div style="font-size: 10px; color: #666;">
                        Unix: ${param.time}
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
                    <div>
                        <div style="color: #888; font-size: 10px;">OPEN</div>
                        <div style="font-weight: bold;">${formatPrice(candleData.open)}</div>
                    </div>
                    <div>
                        <div style="color: #888; font-size: 10px;">HIGH</div>
                        <div style="font-weight: bold; color: #26a69a;">${formatPrice(candleData.high)}</div>
                    </div>
                    <div>
                        <div style="color: #888; font-size: 10px;">LOW</div>
                        <div style="font-weight: bold; color: #ef5350;">${formatPrice(candleData.low)}</div>
                    </div>
                    <div>
                        <div style="color: #888; font-size: 10px;">CLOSE</div>
                        <div style="font-weight: bold;">${formatPrice(candleData.close)}</div>
                    </div>
                </div>
                <div style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.05); border-radius: 4px;">
                    <div style="color: #888; font-size: 10px;">CHANGE</div>
                    <div style="font-weight: bold; color: ${changeColor};">
                        ${changeSymbol}${formatPrice(Math.abs(priceChange))} (${changeSymbol}${priceChangePercent.toFixed(2)}%)
                    </div>
                </div>
                <div style="margin-bottom: 8px;">
                    <div style="color: #888; font-size: 10px;">VOLUME</div>
                    <div style="font-weight: bold;">${formatVolume(volumeData?.value || 0)}</div>
                </div>
                <div style="border-top: 1px solid #444; padding-top: 8px; margin-top: 8px;">
                    <div style="color: #888; font-size: 10px;">CURRENT PRICE</div>
                    <div style="font-weight: bold; color: #ffd700;">${formatPrice(currentPrice || candleData.close)}</div>
                </div>
            `;

            // Position tooltip
            const containerRect = container.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            let left = param.point.x + 15;
            let top = param.point.y + 15;
            
            if (left + 320 > container.clientWidth) {
                left = param.point.x - 320 - 15;
            }
            if (top + tooltipRect.height > container.clientHeight) {
                top = param.point.y - tooltipRect.height - 15;
            }
            
            tooltip.style.left = `${Math.max(0, left)}px`;
            tooltip.style.top = `${Math.max(0, top)}px`;
            tooltip.style.display = 'block';

            // Update price display
            if (currentPrice) {
                priceDisplay.textContent = formatPrice(currentPrice);
                priceDisplay.style.top = `${param.point.y - 15}px`;
                priceDisplay.style.display = 'block';
            }
        });

        // Handle resize
        window.addEventListener('resize', () => {
            chart.applyOptions({
                width: document.getElementById('chart').clientWidth,
            });
        });
    </script>
</body>
</html>
