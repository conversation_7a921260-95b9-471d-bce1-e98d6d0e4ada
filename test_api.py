#!/usr/bin/env python3
"""
Simple API test script to verify the marking tools API is working
"""

import requests
import json
import sys

def test_entry_mark_creation():
    """Test entry mark creation"""
    print("🧪 Testing entry mark creation...")
    
    entry_data = {
        "timestamp": **********.0,
        "price": 45000.50,
        "side": "buy",
        "quantity": 0.1,
        "notes": "Test entry mark",
        "ohlcv_data": {
            "time": **********,
            "open": 44950.0,
            "high": 45100.0,
            "low": 44900.0,
            "close": 45000.0,
            "volume": 1500.0
        },
        "indicator_data": {
            "ema": {"ema_20": 44800.0},
            "rsi": {"rsi_14": 65.5}
        }
    }
    
    try:
        response = requests.post('http://localhost:8000/api/v1/marks/entry', json=entry_data, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Entry mark created successfully!")
            print(f"   ID: {result.get('data', {}).get('id')}")
            print(f"   Side: {result.get('data', {}).get('entry_side')}")
            return result.get('data', {}).get('id')
        else:
            print(f"❌ Entry mark creation failed: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the server is running on http://localhost:8000")
        return None
    except Exception as e:
        print(f"❌ Error testing entry mark: {e}")
        return None

def test_exit_mark_creation(entry_id):
    """Test exit mark creation"""
    if not entry_id:
        print("⏭️  Skipping exit mark test - no entry ID available")
        return False
        
    print("🧪 Testing exit mark creation...")
    
    exit_data = {
        "entry_id": entry_id,
        "timestamp": 1627935600.0,
        "price": 46500.75,
        "quantity": 0.1,
        "notes": "Test exit mark",
        "ohlcv_data": {
            "time": 1627935600,
            "open": 46400.0,
            "high": 46600.0,
            "low": 46350.0,
            "close": 46500.0,
            "volume": 1200.0
        },
        "indicator_data": {
            "ema": {"ema_20": 45900.0},
            "rsi": {"rsi_14": 75.2}
        }
    }
    
    try:
        response = requests.post('http://localhost:8000/api/v1/marks/exit', json=exit_data, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Exit mark created successfully!")
            print(f"   ID: {result.get('data', {}).get('id')}")
            print(f"   Linked to entry: {result.get('data', {}).get('linked_trade_id')}")
            return True
        else:
            print(f"❌ Exit mark creation failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing exit mark: {e}")
        return False

def test_server_health():
    """Test if server is running"""
    print("🏥 Testing server health...")
    
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("✅ Server is healthy")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_toUpperCase_fix():
    """Test the toUpperCase fix with edge cases"""
    print("🧪 Testing toUpperCase() fix with edge cases...")

    test_cases = [
        {"side": "buy", "expected_success": True},
        {"side": "sell", "expected_success": True},
        {"side": "BUY", "expected_success": True},
        {"side": "SELL", "expected_success": True},
        {"side": "", "expected_success": False},  # Should fail validation
    ]

    passed_tests = 0
    total_tests = len(test_cases)

    for i, test_case in enumerate(test_cases):
        print(f"  Test {i+1}: side='{test_case['side']}'")

        entry_data = {
            "timestamp": **********.0 + i,  # Unique timestamp
            "price": 45000.50,
            "side": test_case["side"],
            "quantity": 0.1,
            "notes": f"Test case {i+1}"
        }

        try:
            response = requests.post('http://localhost:8000/api/v1/marks/entry', json=entry_data, timeout=10)
            success = response.status_code == 200

            if success == test_case["expected_success"]:
                print(f"    ✅ Expected result: {success}")
                passed_tests += 1
            else:
                print(f"    ❌ Unexpected result: {success} (expected {test_case['expected_success']})")

        except Exception as e:
            print(f"    ❌ Error: {e}")

    print(f"📊 toUpperCase fix test results: {passed_tests}/{total_tests} passed")
    return passed_tests == total_tests

def test_exit_mark_real_data():
    """Test exit mark creation with real data extraction"""
    print("🧪 Testing exit mark creation with real data...")

    # First create an entry mark
    entry_data = {
        "timestamp": **********.0,
        "price": 45000.50,
        "side": "buy",
        "quantity": 0.1,
        "notes": "Test entry for exit testing",
        "ohlcv_data": {
            "time": **********,
            "open": 44950.0,
            "high": 45100.0,
            "low": 44900.0,
            "close": 45000.0,
            "volume": 1500.0
        },
        "indicator_data": {
            "ema": {"ema_20": 44800.0},
            "rsi": {"rsi_14": 65.5}
        }
    }

    try:
        # Create entry
        entry_response = requests.post('http://localhost:8000/api/v1/marks/entry', json=entry_data, timeout=10)
        if entry_response.status_code != 200:
            print(f"❌ Failed to create entry mark: {entry_response.text}")
            return False

        entry_result = entry_response.json()
        entry_id = entry_result.get('data', {}).get('id')
        print(f"✅ Entry mark created with ID: {entry_id}")

        # Create exit with comprehensive data
        exit_data = {
            "entry_id": entry_id,
            "timestamp": 1627935600.0,
            "price": 46500.75,
            "quantity": 0.1,
            "notes": "Test exit with real data",
            "ohlcv_data": {
                "time": 1627935600,
                "open": 46400.0,
                "high": 46600.0,
                "low": 46350.0,
                "close": 46500.0,
                "volume": 1200.0
            },
            "indicator_data": {
                "ema": {"ema_20": 45900.0},
                "rsi": {"rsi_14": 75.2},
                "macd": {"macd": 150.8, "signal": 140.5}
            }
        }

        exit_response = requests.post('http://localhost:8000/api/v1/marks/exit', json=exit_data, timeout=10)

        if exit_response.status_code == 200:
            exit_result = exit_response.json()
            print(f"✅ Exit mark created successfully!")
            print(f"   Exit ID: {exit_result.get('data', {}).get('id')}")
            print(f"   Linked to entry: {exit_result.get('data', {}).get('linked_trade_id')}")

            # Verify data was stored
            exit_mark_data = exit_result.get('data', {})
            has_ohlcv = 'ohlcv_snapshot' in exit_mark_data
            has_indicators = 'indicator_snapshot' in exit_mark_data

            print(f"   OHLCV data stored: {has_ohlcv}")
            print(f"   Indicator data stored: {has_indicators}")

            return has_ohlcv and has_indicators
        else:
            print(f"❌ Exit mark creation failed: {exit_response.text}")
            return False

    except Exception as e:
        print(f"❌ Error testing exit mark: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 API Testing Suite for Marking Tools")
    print("=" * 50)
    
    # Test 1: Server health
    if not test_server_health():
        print("\n❌ Server is not running. Please start the server first.")
        sys.exit(1)
    
    print()
    
    # Test 2: toUpperCase fix
    touppercase_passed = test_toUpperCase_fix()
    print()

    # Test 3: Entry mark creation
    entry_id = test_entry_mark_creation()
    print()

    # Test 4: Exit mark creation
    exit_passed = test_exit_mark_creation(entry_id)
    print()

    # Test 5: Exit mark real data extraction
    real_data_passed = test_exit_mark_real_data()
    print()
    
    # Summary
    print("📊 Test Summary:")
    print(f"   ✅ Server Health: {'PASS' if True else 'FAIL'}")
    print(f"   ✅ toUpperCase Fix: {'PASS' if touppercase_passed else 'FAIL'}")
    print(f"   ✅ Entry Mark Creation: {'PASS' if entry_id else 'FAIL'}")
    print(f"   ✅ Exit Mark Creation: {'PASS' if exit_passed else 'FAIL'}")
    print(f"   ✅ Exit Real Data Extraction: {'PASS' if real_data_passed else 'FAIL'}")

    all_passed = touppercase_passed and entry_id and exit_passed and real_data_passed
    
    if all_passed:
        print("\n🎉 All API tests passed! The fixes are working correctly.")
        return 0
    else:
        print("\n⚠️  Some API tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
