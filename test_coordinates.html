<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Coordinate Plotting</title>
</head>
<body>
    <h1>Test Coordinate Plotting</h1>
    <div id="output"></div>

    <script>
        // Test data from database
        const testMarks = [
            {
                id: 1,
                mark_type: 'ENTRY',
                entry_side: 'SELL',
                price: 122140.00,
                timestamp: '2025-07-14 14:30:00',
                symbol: 'BTCUSDT',
                timeframe: '15m',
                linked_trade_id: null
            },
            {
                id: 2,
                mark_type: 'EXIT',
                entry_side: null,
                price: 117665.00,
                timestamp: '2025-07-16 00:30:00',
                symbol: 'BTCUSDT',
                timeframe: '15m',
                linked_trade_id: 1
            },
            {
                id: 3,
                mark_type: 'ENTRY',
                entry_side: 'BUY',
                price: 117567.00,
                timestamp: '2025-07-15 22:15:00',
                symbol: 'BTCUSDT',
                timeframe: '15m',
                linked_trade_id: null
            },
            {
                id: 4,
                mark_type: 'EXIT',
                entry_side: null,
                price: 119391.00,
                timestamp: '2025-07-17 05:30:00',
                symbol: 'BTCUSDT',
                timeframe: '15m',
                linked_trade_id: 3
            }
        ];

        function testCoordinatePlotting() {
            const output = document.getElementById('output');
            let html = '<h2>Coordinate Plotting Test</h2>';

            // Helper function to convert timestamp to Unix timestamp for TradingView
            const convertTimestamp = (timestamp) => {
                if (typeof timestamp === 'string') {
                    return Math.floor(new Date(timestamp).getTime() / 1000);
                } else if (typeof timestamp === 'number') {
                    return timestamp > 1000000000000 ? 
                        Math.floor(timestamp / 1000) : 
                        Math.floor(timestamp);
                }
                return timestamp;
            };

            // Separate entry and exit marks
            const entryMarks = testMarks.filter(mark => mark.mark_type === 'ENTRY');
            const exitMarks = testMarks.filter(mark => mark.mark_type === 'EXIT');

            html += '<h3>Entry Marks:</h3>';
            entryMarks.forEach(entry => {
                const unixTime = convertTimestamp(entry.timestamp);
                const readableTime = new Date(entry.timestamp).toLocaleString();
                html += `<p><strong>Entry ${entry.id}</strong> (${entry.entry_side}): ${readableTime} @ $${entry.price}<br>`;
                html += `Unix timestamp: ${unixTime}<br>`;
                html += `Coordinates: (${unixTime}, ${entry.price})</p>`;
            });

            html += '<h3>Exit Marks:</h3>';
            exitMarks.forEach(exit => {
                const unixTime = convertTimestamp(exit.timestamp);
                const readableTime = new Date(exit.timestamp).toLocaleString();
                html += `<p><strong>Exit ${exit.id}</strong> (linked to ${exit.linked_trade_id}): ${readableTime} @ $${exit.price}<br>`;
                html += `Unix timestamp: ${unixTime}<br>`;
                html += `Coordinates: (${unixTime}, ${exit.price})</p>`;
            });

            html += '<h3>Linking Test:</h3>';
            entryMarks.forEach(entry => {
                const matchingExit = exitMarks.find(exit => 
                    exit.linked_trade_id === entry.id &&
                    exit.symbol === entry.symbol &&
                    exit.timeframe === entry.timeframe
                );

                if (matchingExit) {
                    const entryTime = convertTimestamp(entry.timestamp);
                    const exitTime = convertTimestamp(matchingExit.timestamp);
                    const lineColor = entry.entry_side === 'BUY' ? '#4caf50' : '#f44336';
                    
                    html += `<p><strong>✅ LINKED:</strong> Entry ${entry.id} → Exit ${matchingExit.id}<br>`;
                    html += `Line coordinates: (${entryTime}, ${entry.price}) → (${exitTime}, ${matchingExit.price})<br>`;
                    html += `Line color: ${lineColor} (${entry.entry_side === 'BUY' ? 'Green for BUY' : 'Red for SELL'})<br>`;
                    
                    // Calculate P&L
                    let pnl;
                    if (entry.entry_side === 'BUY') {
                        pnl = matchingExit.price - entry.price;
                    } else { // SELL
                        pnl = entry.price - matchingExit.price;
                    }
                    const pnlPct = (pnl / entry.price) * 100;
                    html += `P&L: $${pnl.toFixed(2)} (${pnlPct.toFixed(2)}%)</p>`;
                } else {
                    html += `<p><strong>❌ UNLINKED:</strong> Entry ${entry.id} has no matching exit</p>`;
                }
            });

            output.innerHTML = html;
        }

        // Run test when page loads
        window.onload = testCoordinatePlotting;
    </script>
</body>
</html>
