#!/usr/bin/env python3
"""
Integration test script for marking tools fixes
Tests both frontend JavaScript fixes and backend API functionality
"""

import asyncio
import json
import subprocess
import sys
import os
from pathlib import Path

class IntegrationTester:
    def __init__(self):
        self.results = []
        self.project_root = Path(__file__).parent
        
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        result = {
            "test": test_name,
            "passed": passed,
            "message": message,
            "timestamp": str(asyncio.get_event_loop().time())
        }
        self.results.append(result)
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        return passed

    def test_frontend_files_exist(self):
        """Test that frontend files exist and are accessible"""
        print("\n=== Testing Frontend Files ===")
        
        files_to_check = [
            "frontend/static/js/marking-tools.js",
            "frontend/static/js/tests/marking-tools-tests.js",
            "frontend/static/test-runner.html"
        ]
        
        all_passed = True
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            exists = full_path.exists()
            all_passed = all_passed and exists
            self.log_test(f"File exists: {file_path}", exists, 
                         "Found" if exists else "Missing")
        
        return self.log_test("Frontend Files Check", all_passed, 
                           f"Checked {len(files_to_check)} files")

    def test_backend_files_exist(self):
        """Test that backend files exist and are accessible"""
        print("\n=== Testing Backend Files ===")
        
        files_to_check = [
            "backend/app/api/marks.py",
            "backend/app/schemas/marks.py",
            "backend/tests/test_marks_api.py"
        ]
        
        all_passed = True
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            exists = full_path.exists()
            all_passed = all_passed and exists
            self.log_test(f"File exists: {file_path}", exists, 
                         "Found" if exists else "Missing")
        
        return self.log_test("Backend Files Check", all_passed, 
                           f"Checked {len(files_to_check)} files")

    def test_javascript_syntax(self):
        """Test JavaScript syntax validity"""
        print("\n=== Testing JavaScript Syntax ===")
        
        js_files = [
            "frontend/static/js/marking-tools.js",
            "frontend/static/js/tests/marking-tools-tests.js"
        ]
        
        all_passed = True
        for js_file in js_files:
            try:
                # Use Node.js to check syntax if available
                result = subprocess.run(
                    ["node", "-c", str(self.project_root / js_file)],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                passed = result.returncode == 0
                message = "Valid syntax" if passed else f"Syntax error: {result.stderr}"
            except (subprocess.TimeoutExpired, FileNotFoundError):
                # Fallback: basic file reading test
                try:
                    with open(self.project_root / js_file, 'r') as f:
                        content = f.read()
                    passed = len(content) > 0 and 'function' in content
                    message = "File readable, contains functions" if passed else "File issues"
                except Exception as e:
                    passed = False
                    message = f"Error reading file: {e}"
            
            all_passed = all_passed and passed
            self.log_test(f"JS Syntax: {js_file}", passed, message)
        
        return self.log_test("JavaScript Syntax Check", all_passed, 
                           f"Checked {len(js_files)} files")

    def test_python_syntax(self):
        """Test Python syntax validity"""
        print("\n=== Testing Python Syntax ===")
        
        py_files = [
            "backend/app/api/marks.py",
            "backend/app/schemas/marks.py",
            "backend/tests/test_marks_api.py"
        ]
        
        all_passed = True
        for py_file in py_files:
            try:
                # Compile Python file to check syntax
                with open(self.project_root / py_file, 'r') as f:
                    content = f.read()
                compile(content, py_file, 'exec')
                passed = True
                message = "Valid Python syntax"
            except SyntaxError as e:
                passed = False
                message = f"Syntax error: {e}"
            except Exception as e:
                passed = False
                message = f"Error: {e}"
            
            all_passed = all_passed and passed
            self.log_test(f"Python Syntax: {py_file}", passed, message)
        
        return self.log_test("Python Syntax Check", all_passed, 
                           f"Checked {len(py_files)} files")

    def test_fix_implementations(self):
        """Test that specific fixes are implemented"""
        print("\n=== Testing Fix Implementations ===")
        
        # Check for toUpperCase() fixes
        marking_tools_path = self.project_root / "frontend/static/js/marking-tools.js"
        try:
            with open(marking_tools_path, 'r') as f:
                content = f.read()
            
            # Check for safe toUpperCase patterns
            safe_patterns = [
                "side && typeof side === 'string' ? side.charAt(0).toUpperCase()",
                "entryData.side ? entryData.side.toUpperCase() : 'BUY'",
                "if (!side || typeof side !== 'string')"
            ]
            
            patterns_found = 0
            for pattern in safe_patterns:
                if pattern in content:
                    patterns_found += 1
            
            touppercase_fixed = patterns_found >= 2
            self.log_test("toUpperCase() Safety Fixes", touppercase_fixed, 
                         f"Found {patterns_found}/3 safety patterns")
            
            # Check for normalizeOHLCVData method
            normalize_method_exists = "normalizeOHLCVData" in content and "function" in content
            self.log_test("normalizeOHLCVData Method", normalize_method_exists, 
                         "Method exists" if normalize_method_exists else "Method missing")
            
            # Check for enhanced error handling
            enhanced_error_handling = "data_quality" in content and "ohlcv_available" in content
            self.log_test("Enhanced Error Handling", enhanced_error_handling, 
                         "Enhanced validation found" if enhanced_error_handling else "Basic validation only")
            
            all_fixes = touppercase_fixed and normalize_method_exists and enhanced_error_handling
            
        except Exception as e:
            all_fixes = False
            self.log_test("Fix Implementation Check", False, f"Error reading file: {e}")
        
        return self.log_test("Fix Implementations", all_fixes, 
                           "All critical fixes implemented")

    def test_api_schema_validation(self):
        """Test API schema definitions"""
        print("\n=== Testing API Schema Validation ===")
        
        try:
            # Add backend to path for imports
            backend_path = str(self.project_root / "backend")
            if backend_path not in sys.path:
                sys.path.insert(0, backend_path)
            
            # Import and test schemas
            from app.schemas.marks import MarkEntry, MarkExit
            
            # Test valid entry data
            valid_entry = {
                "timestamp": 1627849200.0,
                "price": 45000.50,
                "side": "buy",
                "quantity": 0.1,
                "notes": "Test entry"
            }
            
            entry = MarkEntry(**valid_entry)
            entry_valid = entry.side == "buy" and entry.quantity == 0.1
            self.log_test("MarkEntry Schema", entry_valid, "Valid entry created")
            
            # Test valid exit data
            valid_exit = {
                "entry_id": 1,
                "timestamp": 1627935600.0,
                "price": 46500.75,
                "quantity": 0.1,
                "notes": "Test exit"
            }
            
            exit_mark = MarkExit(**valid_exit)
            exit_valid = exit_mark.entry_id == 1 and exit_mark.quantity == 0.1
            self.log_test("MarkExit Schema", exit_valid, "Valid exit created")
            
            schema_tests_passed = entry_valid and exit_valid
            
        except Exception as e:
            schema_tests_passed = False
            self.log_test("Schema Import Error", False, f"Error: {e}")
        
        return self.log_test("API Schema Validation", schema_tests_passed, 
                           "Schema validation working")

    def test_comprehensive_data_structure(self):
        """Test comprehensive data structure format"""
        print("\n=== Testing Comprehensive Data Structure ===")
        
        # Define expected structure
        expected_structure = {
            "symbol": "BTCUSDT",
            "timeframe": "15m",
            "entry": {
                "timestamp": "2021-08-01T15:00:00.000Z",
                "entry_side": "Buy",
                "price": 45000.50,
                "ohlcv": {
                    "time": 1627849200,
                    "open": 44950.0,
                    "high": 45100.0,
                    "low": 44900.0,
                    "close": 45000.0,
                    "volume": 1500.0
                },
                "indicators": {
                    "ema": {"ema_20": 44800.0},
                    "rsi": {"rsi_14": 65.5}
                }
            },
            "exit": {
                "timestamp": "2021-08-02T15:00:00.000Z",
                "unix_timestamp": 1627935600,
                "price": 46500.75,
                "ohlcv": {},
                "indicators": {},
                "data_quality": {
                    "ohlcv_available": True,
                    "indicators_available": True,
                    "timestamp_valid": True,
                    "price_valid": True
                }
            },
            "pnl": {
                "absolute": 150.025,
                "percentage": 3.33,
                "quantity": 0.1,
                "entry_price": 45000.50,
                "exit_price": 46500.75,
                "side": "buy"
            }
        }
        
        # Validate structure
        structure_valid = True
        required_keys = ["symbol", "timeframe", "entry", "exit", "pnl"]
        
        for key in required_keys:
            if key not in expected_structure:
                structure_valid = False
                break
        
        # Validate nested structures
        if "data_quality" not in expected_structure["exit"]:
            structure_valid = False
        
        if "absolute" not in expected_structure["pnl"]:
            structure_valid = False
        
        self.log_test("Data Structure Validation", structure_valid, 
                     "Comprehensive structure valid" if structure_valid else "Structure issues")
        
        return structure_valid

    async def run_all_tests(self):
        """Run all integration tests"""
        print("🧪 Starting Integration Test Suite for Marking Tools Fixes\n")
        
        tests = [
            self.test_frontend_files_exist,
            self.test_backend_files_exist,
            self.test_javascript_syntax,
            self.test_python_syntax,
            self.test_fix_implementations,
            self.test_api_schema_validation,
            self.test_comprehensive_data_structure
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed_tests += 1
            except Exception as e:
                self.log_test(f"Test Error: {test.__name__}", False, f"Exception: {e}")
        
        print(f"\n📊 Integration Test Results: {passed_tests}/{total_tests} tests passed")
        print(f"📋 Individual checks: {len([r for r in self.results if r['passed']])}/{len(self.results)} passed")
        
        # Generate summary report
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "total_checks": len(self.results),
                "passed_checks": len([r for r in self.results if r['passed']]),
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%"
            },
            "results": self.results
        }
        
        # Save report
        report_path = self.project_root / "integration_test_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Report saved to: {report_path}")
        
        return passed_tests == total_tests

async def main():
    """Main test runner"""
    tester = IntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 All integration tests passed! The fixes are working correctly.")
        return 0
    else:
        print("\n⚠️  Some integration tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
