<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Trade Lines</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1e53e5;
        }
        
        button.success {
            background: #4caf50;
        }
        
        button.danger {
            background: #f44336;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #26a69a; }
        .log-error { color: #ef5350; }
        .log-warning { color: #ff9800; }
        .log-info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Trade Lines Test</h1>
        
        <div class="test-section">
            <h2>Test Entry-Exit Line Connections</h2>
            <p>This test verifies that trade lines are properly drawn between entry and exit marks.</p>
            
            <div class="controls">
                <button id="check-marks">1. Check Existing Marks</button>
                <button id="create-test-entry" class="success">2. Create Test Entry</button>
                <button id="create-test-exit" class="danger">3. Create Test Exit</button>
                <button id="verify-lines">4. Verify Lines</button>
                <button id="clear-test-marks">Clear Test Marks</button>
                <button id="clear-log">Clear Log</button>
            </div>
            
            <div class="status" id="log-output">
                <div class="log-info">🔄 Ready to test trade lines...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Trade Line Features</h2>
            <ul>
                <li><strong>Green Lines</strong>: BUY entries (entry to exit)</li>
                <li><strong>Red Lines</strong>: SELL entries (entry to exit)</li>
                <li><strong>Line Width</strong>: 2px solid lines</li>
                <li><strong>Auto-linking</strong>: Exit marks link to entries via <code>linked_trade_id</code></li>
                <li><strong>Real-time Updates</strong>: Lines update when marks are added/removed</li>
            </ul>
        </div>
    </div>

    <script>
        let testEntryId = null;
        let testExitId = null;
        
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logOutput.appendChild(entry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        async function checkExistingMarks() {
            try {
                log('🔍 Checking existing marks...');
                
                const response = await fetch('/api/v1/marks');
                const result = await response.json();
                
                if (result.success && result.data) {
                    log(`📊 Found ${result.data.length} existing marks`, 'success');
                    
                    const entryMarks = result.data.filter(mark => mark.mark_type === 'ENTRY');
                    const exitMarks = result.data.filter(mark => mark.mark_type === 'EXIT');
                    
                    log(`  - Entry marks: ${entryMarks.length}`);
                    log(`  - Exit marks: ${exitMarks.length}`);
                    
                    // Check for linked trades
                    let linkedTrades = 0;
                    entryMarks.forEach(entry => {
                        const linkedExit = exitMarks.find(exit => exit.linked_trade_id === entry.id);
                        if (linkedExit) {
                            linkedTrades++;
                            log(`  - Linked trade: Entry ${entry.id} → Exit ${linkedExit.id}`, 'success');
                        }
                    });
                    
                    log(`📈 Total linked trades: ${linkedTrades}`, linkedTrades > 0 ? 'success' : 'warning');
                    
                    if (linkedTrades > 0) {
                        log('✅ Trade lines should be visible on the chart', 'success');
                    } else {
                        log('⚠️ No linked trades found - no lines will be drawn', 'warning');
                    }
                    
                } else {
                    log('❌ Failed to fetch marks', 'error');
                }
                
            } catch (error) {
                log(`❌ Error checking marks: ${error.message}`, 'error');
            }
        }
        
        async function createTestEntry() {
            try {
                log('📝 Creating test entry mark...');
                
                const entryData = {
                    symbol: 'BTCUSDT',
                    timeframe: '15m',
                    mark_type: 'entry',
                    entry_side: 'buy',
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                    price: 45000.50
                };
                
                const response = await fetch('/api/v1/trades/mark', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(entryData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testEntryId = result.data.mark_id;
                    log(`✅ Test entry created with ID: ${testEntryId}`, 'success');
                    log(`  - Price: $${entryData.price}`, 'info');
                    log(`  - Side: ${entryData.entry_side.toUpperCase()}`, 'info');
                    log(`  - Time: ${entryData.timestamp}`, 'info');
                } else {
                    log(`❌ Failed to create entry: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Error creating entry: ${error.message}`, 'error');
            }
        }
        
        async function createTestExit() {
            if (!testEntryId) {
                log('❌ No test entry found. Create an entry first.', 'error');
                return;
            }
            
            try {
                log('📝 Creating test exit mark...');
                
                const exitData = {
                    symbol: 'BTCUSDT',
                    timeframe: '15m',
                    mark_type: 'exit',
                    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
                    price: 45500.75,
                    linked_trade_id: testEntryId
                };
                
                const response = await fetch('/api/v1/trades/mark', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(exitData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    testExitId = result.data.mark_id;
                    log(`✅ Test exit created with ID: ${testExitId}`, 'success');
                    log(`  - Price: $${exitData.price}`, 'info');
                    log(`  - Linked to entry: ${testEntryId}`, 'info');
                    log(`  - Time: ${exitData.timestamp}`, 'info');
                    
                    const profit = ((exitData.price - 45000.50) / 45000.50 * 100).toFixed(2);
                    log(`  - Profit: ${profit}% (should show GREEN line)`, profit > 0 ? 'success' : 'error');
                    
                    log('🔗 Trade line should now be visible on the chart!', 'success');
                } else {
                    log(`❌ Failed to create exit: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Error creating exit: ${error.message}`, 'error');
            }
        }
        
        async function verifyLines() {
            try {
                log('🔍 Verifying trade lines...');
                
                // Check if marking tools exist
                if (typeof window.markingTools === 'undefined') {
                    log('❌ Marking tools not found on main page', 'error');
                    log('💡 This test should be run on the main trading page', 'warning');
                    return;
                }
                
                const markingTools = window.markingTools;
                
                if (!markingTools) {
                    log('❌ Marking tools not initialized', 'error');
                    return;
                }
                
                log(`📊 Trade lines map size: ${markingTools.tradeLines.size}`, 'info');
                log(`📊 Marks map size: ${markingTools.marks.size}`, 'info');
                
                if (markingTools.tradeLines.size > 0) {
                    log('✅ Trade lines found!', 'success');
                    markingTools.tradeLines.forEach((lineSeries, lineId) => {
                        log(`  - Line ID: ${lineId}`, 'success');
                    });
                } else {
                    log('⚠️ No trade lines found', 'warning');
                    log('💡 Try refreshing the page to reload marks', 'info');
                }
                
            } catch (error) {
                log(`❌ Error verifying lines: ${error.message}`, 'error');
            }
        }
        
        async function clearTestMarks() {
            if (!testEntryId && !testExitId) {
                log('⚠️ No test marks to clear', 'warning');
                return;
            }
            
            try {
                log('🗑️ Clearing test marks...');
                
                const marksToDelete = [];
                if (testEntryId) marksToDelete.push(testEntryId);
                if (testExitId) marksToDelete.push(testExitId);
                
                for (const markId of marksToDelete) {
                    const response = await fetch(`/api/v1/marks/${markId}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        log(`✅ Deleted mark ${markId}`, 'success');
                    } else {
                        log(`❌ Failed to delete mark ${markId}`, 'error');
                    }
                }
                
                testEntryId = null;
                testExitId = null;
                log('✅ Test marks cleared', 'success');
                
            } catch (error) {
                log(`❌ Error clearing marks: ${error.message}`, 'error');
            }
        }
        
        // Event listeners
        document.getElementById('check-marks').addEventListener('click', checkExistingMarks);
        document.getElementById('create-test-entry').addEventListener('click', createTestEntry);
        document.getElementById('create-test-exit').addEventListener('click', createTestExit);
        document.getElementById('verify-lines').addEventListener('click', verifyLines);
        document.getElementById('clear-test-marks').addEventListener('click', clearTestMarks);
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('log-output').innerHTML = '<div class="log-info">🔄 Log cleared...</div>';
        });
        
        log('🚀 Trade lines test ready');
        log('💡 Run this test on the main trading page for full functionality');
    </script>
</body>
</html>
